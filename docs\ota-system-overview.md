# OTA订单处理系统 - 详细概览

## 系统版本
**OTA订单处理系统 v2.0.0**  
*基于DeepSeek+Gemini的轻量化订单解析系统*  
**最后更新：** 2024-12-19

---

## 一、系统架构概览

### 🔄 双层AI处理架构
- **主要AI服务：** DeepSeek (优先级1，15秒超时)
- **备用AI服务：** Gemini 2.0 Flash (优先级2，30秒超时)
- **图片识别：** Google Vision API (专用于OCR识别)

### 📊 智能处理流程
1. **OTA类型检测** → 2. **关键词匹配** → 3. **LLM处理** → 4. **格式标准化** → 5. **质量验证**

---

## 二、OTA类型详细配置

### 🏢 1. Chong Dealer (重庆经销商)

#### ✅ **关键词识别模式**
- **最低匹配数：** 1个关键词
- **置信度要求：** 0.9 (90%)
- **优先级：** 1 (最高)

#### 📝 **关键词列表 (16个模式)**
```javascript
关键词模式识别：
1. 'CHONG 车头'               - 主要发送者标识
2. '收单&进单'                - 群组标识  
3. '\\*京鱼\\*'               - 处理员标记
4. '\\*野马\\*'               - 处理员标记
5. '\\*小野马\\*'             - 处理员标记
6. '\\*kenny\\*'              - 处理员标记
7. '\\*迹象\\*'               - 处理员标记
8. '\\*鲸鱼\\*'               - 处理员标记
9. '用车地点[:：]'            - 地点信息标识
10. '用车时间[:：]'           - 时间信息标识
11. '客人姓名[:：]'           - 客户信息标识
12. '接送机类型[:：]'         - 服务类型标识
13. '结算[:：]'               - 价格信息标识
14. '价格[:：].*?Rm'         - 马币价格匹配
15. '举牌'                    - 服务特殊要求
16. '机场转乘'                - 特殊服务类型
17. '包车.*?小时'             - 包车服务识别
```

#### 🎯 **预设字段内容**
```yaml
默认配置：
  - 币种: RM (马币)
  - 时区: UTC+8 (马来西亚时间)
  - 默认机场: KLIA (吉隆坡国际机场)
  - 服务区域: 吉隆坡地区
  - 默认车型: 根据人数自动匹配 (1-4人→5座，5-7人→7座，8+人→商务车)
```

#### ⚙️ **订单处理规则**

**📅 日期处理逻辑：**
- 以当前日期 `2025-06-02` 为基准
- 多重验算确保输出为未来日期
- 自动顺延过期日期到下个月同一天
- 处理跨年和特殊日期 (如2月30日)

**⏰ 时间计算规则：**
```javascript
接机 (Pickup):
  - 使用航班到达时间作为订单时间
  - pickup = "KLIA" 
  - drop = "英文酒店名"

送机 (Dropoff):
  - 航班起飞时间 - 3.5小时 = 订单时间
  - pickup = "英文酒店名"
  - drop = "KLIA"
```

**🏨 酒店名称处理：**
- 中文酒店名自动转换为标准英文名
- 参考数据源：Ctrip, Booking.com, Google
- 保持英文名称的标准格式

**📋 输出字段格式：**
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
```

---

### 🤖 2. Auto Detect (自动识别)

#### ✅ **识别特点**
- **处理方式：** 纯LLM智能分析
- **适用场景：** 无明确OTA特征的订单
- **备用策略：** 当其他类型检测失败时使用

#### 📝 **处理规则**
```yaml
智能分析项目：
  - 订单内容语言识别 (中文/英文/混合)
  - 服务类型判断 (接机/送机/包车/点对点)
  - 时间格式智能解析
  - 地点信息标准化
  - 联系信息生成
```

#### 🎯 **预设字段内容**
```yaml
自动生成字段：
  - OTA参考号: OTA-YYYYMMDD-XXX
  - 联系电话: 基于(日期+时间+航班+姓名)算法生成
  - 服务分类: 自动匹配到子分类
  - 车型建议: 根据人数智能推荐
```

#### ⚙️ **处理规则**
**📊 输出字段格式：**
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
ota_reference_number: [OTA参考号]
```

---

### 🌐 3. Other OTA (通用处理)

#### ✅ **适用范围**
- 其他未识别的OTA类型
- 特殊格式订单
- 混合类型订单

#### 📝 **处理特点**
```yaml
通用处理能力：
  - 灵活的格式适应
  - 多语言支持
  - 智能信息补全
  - 错误标记和建议
```

#### 🎯 **预设字段内容**
```yaml
标准化处理：
  - 日期格式: YYYY-MM-DD
  - 时间格式: HH:MM (24小时制)
  - 地点标准化: 英文标准名称
  - 质量控制: 逻辑验证和错误标记
```

#### ⚙️ **处理规则**
**📊 输出字段格式：**
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [服务类型：接机/送机]
other: [其他信息和备注]
ota_reference_number: [OTA参考号]
processing_notes: [处理说明]
```

---

## 三、智能选择规则配置

### 🚗 **车型自动匹配**
```javascript
根据乘客人数智能选择：
1-4人  → 1号车型 (轿车/小型车)
5-7人  → 2号车型 (7座MPV)
8-10人 → 3号车型 (商务车/中巴)
```

### 📂 **服务分类映射**
```javascript
服务类型自动分类：
接机     → 1号子分类
送机     → 2号子分类  
包车     → 3号子分类
点对点   → 4号子分类
机场转乘 → 5号子分类
```

### 👥 **后台用户分配**
```javascript
根据OTA类型分配处理员：
chong_dealer → 2号后台用户
gomyhire     → 1号后台用户
other        → 默认用户
```

---

## 四、图片OCR识别能力

### 📸 **Google Vision API配置**
```yaml
支持功能：
  - TEXT_DETECTION: 普通文字识别
  - DOCUMENT_TEXT_DETECTION: 文档文字识别
  - LABEL_DETECTION: 标签识别
  - OBJECT_LOCALIZATION: 对象定位

技术规格：
  - 最大文件大小: 10MB
  - 支持格式: JPEG, PNG, GIF, WebP
  - 最大文件数: 10个
  - 处理超时: 15秒
```

### 🔍 **OCR处理流程**
1. **图片上传** → 2. **Vision API分析** → 3. **文字提取** → 4. **结构化解析** → 5. **订单信息生成**

### 📋 **OCR输出格式**
```
=== OCR识别原始内容 ===
[图片中识别到的原始文字内容]

=== 提取的订单信息 ===
[标准化的订单字段]

=== 处理说明 ===
- OCR置信度: [高/中/低]
- 可能的错误: [识别错误提醒]
- 建议检查: [需要人工确认的项目]
```

---

## 五、性能与监控

### ⚡ **性能目标**
```yaml
处理时间目标：
  - OTA检测: < 100ms
  - LLM处理: < 15s
  - 格式转换: < 50ms
  - 总处理时间: < 17s

监控配置：
  - 性能监控: 启用
  - 慢请求记录: > 20s
  - 错误重试: 最多3次
```

### 🔄 **容错机制**
```yaml
故障转移链：
1. DeepSeek API (主要)
2. Gemini API (备用)  
3. 本地解析器 (兜底)

重试策略：
  - 最大重试次数: 3次
  - 重试间隔: 1s, 2s, 4s (指数退避)
  - 超时处理: 自动切换到备用服务
```

---

## 六、系统优势总结

### 🎯 **核心优势**
1. **双AI架构** - DeepSeek + Gemini 确保高可用性
2. **智能识别** - 16种关键词模式精准识别Chong Dealer订单
3. **自动纠错** - 智能日期修正和时间计算
4. **多格式支持** - 文本、图片、混合格式全支持
5. **实时监控** - 完整的性能监控和错误追踪

### 📈 **处理能力**
- **订单识别准确率：** > 95%
- **处理速度：** < 17秒/订单
- **支持语言：** 中文、英文、混合格式
- **并发处理：** 支持多订单批量处理
- **错误恢复：** 三层容错机制

---

*最后更新时间：2025-06-02*  
*系统版本：v2.0.0*
