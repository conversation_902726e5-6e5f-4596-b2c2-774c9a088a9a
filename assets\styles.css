/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
}

.success-message {
    color: #27ae60;
    margin-top: 10px;
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

/* 登录表单 */
#loginForm {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

button:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 15px 30px;
    font-size: 18px;
    font-weight: 600;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 主应用界面 */
#mainApp {
    min-height: 100vh;
    background: #f8f9fa;
}

header {
    background: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #667eea;
    font-size: 28px;
    font-weight: 700;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* LLM 状态指示器 */
.llm-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: #f8f9fa;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    position: relative;
    min-width: 120px;
}

.llm-status-indicator:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* DeepSeek 指示器特殊样式 */
.deepseek-indicator {
    border-left: 3px solid #4CAF50;
}

.deepseek-indicator .llm-label {
    background: #4CAF50;
    color: white;
}

/* Gemini 指示器特殊样式 */
.gemini-indicator {
    border-left: 3px solid #2196F3;
}

.gemini-indicator .llm-label {
    background: #2196F3;
    color: white;
}

.status-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #f39c12;
    box-shadow: 0 0 8px rgba(243, 156, 18, 0.6);
    animation: pulse 2s infinite;
    transition: all 0.3s ease;
}

.status-light.connected {
    background: #27ae60;
    box-shadow: 0 0 8px rgba(39, 174, 96, 0.6);
    animation: none;
}

.status-light.disconnected {
    background: #e74c3c;
    box-shadow: 0 0 8px rgba(231, 76, 60, 0.6);
    animation: none;
}

.status-light.checking {
    background: #f39c12;
    box-shadow: 0 0 8px rgba(243, 156, 18, 0.6);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.status-text {
    font-size: 11px;
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
    flex: 1;
}

.llm-label {
    font-size: 9px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

#userInfo {
    color: #666;
    font-weight: 500;
}

#logoutBtn {
    background: #e74c3c;
    padding: 8px 16px;
    font-size: 14px;
}

#logoutBtn:hover {
    background: #c0392b;
}

/* 主内容区域 */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.section {
    background: white;
    margin-bottom: 30px;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

/* 标签页 */
.input-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    font-weight: 600;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 文字输入区域 */
#orderText {
    width: 100%;
    min-height: 200px;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    resize: vertical;
    transition: border-color 0.3s;
}

#orderText:focus {
    outline: none;
    border-color: #667eea;
}

/* 图片上传区域 */
.upload-area {
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e8f0ff;
}

#imageFile {
    display: none;
}

.image-preview {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.image-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

/* OTA选择 */
.ota-selection {
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ota-selection label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #555;
}

#otaSelect {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background: white;
}

/* 结果预览 */
.result-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
}

/* 智能选择控制器 */
.smart-selection-controls {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.smart-selection-controls h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.selection-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.selection-row .form-group {
    margin-bottom: 0;
}

.selection-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.selection-row select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.selection-row select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.selection-row select:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

.result-content {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #ddd;
    min-height: 200px;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    line-height: 1.6;
}

.order-item {
    background: white;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-item h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 18px;
}

.order-field {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.order-field label {
    font-weight: 600;
    color: #555;
    width: 120px;
    flex-shrink: 0;
}

.order-field input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 10px;
}

/* 操作按钮 */
.action-buttons {
    margin-top: 25px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

#exportBtn {
    background: #27ae60;
}

#exportBtn:hover {
    background: #229954;
}

/* 状态显示 */
#statusContent {
    padding: 20px;
}

.status-item {
    background: #f8f9fa;
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.status-item.error {
    border-left-color: #e74c3c;
    background: #fdf2f2;
}

.status-item.pending {
    border-left-color: #f39c12;
    background: #fef9e7;
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 图片分析结果样式 */
.image-analysis-result {
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    padding: 15px;
}

.image-analysis-result h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.analysis-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.analysis-tab-btn {
    background: none;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.analysis-tab-btn.active {
    border-bottom-color: #667eea;
    color: #667eea;
    font-weight: bold;
}

.analysis-tab-btn:hover {
    background: #f0f0f0;
}

.analysis-content {
    display: none;
}

.analysis-content.active {
    display: block;
}

#extractedTextContent {
    width: 100%;
    height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: white;
    resize: vertical;
}

.analysis-section {
    margin-bottom: 20px;
}

.analysis-section h4 {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 16px;
}

#labelsList, #objectsList {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.label-tag, .object-tag {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.confidence-score {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
}

.image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 10px 8px 5px;
    font-size: 12px;
}

.file-name {
    display: block;
    font-weight: bold;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-controls {
        flex-direction: column;
        gap: 10px;
    }

    .llm-status-indicator {
        align-self: center;
        min-width: 100px;
    }

    .status-text {
        font-size: 10px;
    }

    .llm-label {
        font-size: 8px;
        padding: 1px 4px;
    }

    .user-info {
        justify-content: center;
    }
    
    main {
        padding: 20px 15px;
    }
    
    .section {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .result-controls {
        flex-direction: column;
    }

    .selection-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .smart-selection-controls {
        padding: 15px;
        margin: 15px 0;
    }

    .order-field {
        flex-direction: column;
        align-items: flex-start;
    }

    .order-field label {
        width: auto;
        margin-bottom: 5px;
    }

    .order-field input {
        margin-left: 0;
        width: 100%;
    }
}