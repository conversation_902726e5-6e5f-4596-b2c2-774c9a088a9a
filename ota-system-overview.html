<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - 详细概览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header .version {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .header .description {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .ota-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .ota-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .ota-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .ota-header {
            padding: 20px;
            color: white;
            font-weight: bold;
            font-size: 1.3em;
        }

        .chong-dealer .ota-header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .auto-detect .ota-header {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }

        .other-ota .ota-header {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .ota-content {
            padding: 25px;
        }

        .feature-section {
            margin-bottom: 20px;
        }

        .feature-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-title::before {
            content: "●";
            color: #3498db;
            font-size: 1.2em;
        }

        .keyword-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 8px;
            margin-bottom: 15px;
        }

        .keyword-tag {
            background: #ecf0f1;
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 0.85em;
            text-align: center;
            border: 1px solid #bdc3c7;
        }

        .processing-rules {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            margin: 10px 0;
        }

        .output-format {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .architecture-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .architecture-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }

        .flow-step {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            min-width: 120px;
            position: relative;
        }

        .flow-step:not(:last-child)::after {
            content: "→";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: bold;
        }

        .highlight {
            background: linear-gradient(45deg, #f1c40f, #f39c12);
            color: #2c3e50;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .ota-grid {
                grid-template-columns: 1fr;
            }
            
            .architecture-flow {
                flex-direction: column;
            }
            
            .flow-step:not(:last-child)::after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 系统头部 -->
        <div class="header">
            <h1>🚀 OTA订单处理系统</h1>
            <div class="version">v2.0.0</div>
            <div class="description">基于DeepSeek+Gemini的轻量化订单解析系统</div>
        </div>

        <!-- 系统架构流程 -->
        <div class="architecture-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">🔄 智能处理流程</h2>
            <div class="architecture-flow">
                <div class="flow-step">OTA类型检测</div>
                <div class="flow-step">关键词匹配</div>
                <div class="flow-step">LLM处理</div>
                <div class="flow-step">格式标准化</div>
                <div class="flow-step">质量验证</div>
            </div>
        </div>

        <!-- 性能统计 -->
        <div class="stats-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">📊 系统性能指标</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">&gt;95%</div>
                    <div class="stat-label">识别准确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">&lt;17s</div>
                    <div class="stat-label">处理速度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">16</div>
                    <div class="stat-label">关键词模式</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">3层</div>
                    <div class="stat-label">容错机制</div>
                </div>
            </div>
        </div>

        <!-- OTA类型详细信息 -->
        <div class="ota-grid">
            <!-- Chong Dealer -->
            <div class="ota-card chong-dealer">
                <div class="ota-header">
                    🏢 Chong Dealer (重庆经销商)
                </div>
                <div class="ota-content">
                    <div class="feature-section">
                        <div class="feature-title">关键词识别 (16个模式)</div>
                        <div class="keyword-grid">
                            <div class="keyword-tag">CHONG 车头</div>
                            <div class="keyword-tag">收单&进单</div>
                            <div class="keyword-tag">*京鱼*</div>
                            <div class="keyword-tag">*野马*</div>
                            <div class="keyword-tag">*小野马*</div>
                            <div class="keyword-tag">*kenny*</div>
                            <div class="keyword-tag">*迹象*</div>
                            <div class="keyword-tag">*鲸鱼*</div>
                            <div class="keyword-tag">用车地点:</div>
                            <div class="keyword-tag">用车时间:</div>
                            <div class="keyword-tag">客人姓名:</div>
                            <div class="keyword-tag">接送机类型:</div>
                            <div class="keyword-tag">结算:</div>
                            <div class="keyword-tag">价格:.*Rm</div>
                            <div class="keyword-tag">举牌</div>
                            <div class="keyword-tag">机场转乘</div>
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">处理规则</div>
                        <div class="processing-rules">
                            <strong>日期处理：</strong>多重验算，自动顺延过期日期<br>
                            <strong>接机时间：</strong>使用航班到达时间<br>
                            <strong>送机时间：</strong>起飞时间减去3.5小时<br>
                            <strong>酒店名称：</strong>中文自动转英文<br>
                            <strong>最低匹配：</strong>1个关键词 (置信度90%)
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">输出格式</div>
                        <div class="output-format">
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
                        </div>
                    </div>
                </div>
            </div>

            <!-- Auto Detect -->
            <div class="ota-card auto-detect">
                <div class="ota-header">
                    🤖 Auto Detect (自动识别)
                </div>
                <div class="ota-content">
                    <div class="feature-section">
                        <div class="feature-title">智能分析能力</div>
                        <div class="processing-rules">
                            <strong>语言识别：</strong>中文/英文/混合格式<br>
                            <strong>服务类型：</strong>接机/送机/包车/点对点<br>
                            <strong>时间解析：</strong>智能格式识别<br>
                            <strong>地点标准化：</strong>自动英文转换<br>
                            <strong>信息生成：</strong>联系方式自动生成
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">自动生成字段</div>
                        <div class="keyword-grid">
                            <div class="keyword-tag">OTA参考号</div>
                            <div class="keyword-tag">联系电话</div>
                            <div class="keyword-tag">服务分类</div>
                            <div class="keyword-tag">车型建议</div>
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">输出格式</div>
                        <div class="output-format">
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
ota_reference_number: [OTA参考号]
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other OTA -->
            <div class="ota-card other-ota">
                <div class="ota-header">
                    🌐 Other OTA (通用处理)
                </div>
                <div class="ota-content">
                    <div class="feature-section">
                        <div class="feature-title">通用处理能力</div>
                        <div class="processing-rules">
                            <strong>格式适应：</strong>灵活处理各种格式<br>
                            <strong>多语言支持：</strong>国际化订单处理<br>
                            <strong>智能补全：</strong>缺失信息自动填充<br>
                            <strong>错误标记：</strong>问题订单智能提醒<br>
                            <strong>质量控制：</strong>逻辑验证和建议
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">标准化处理</div>
                        <div class="keyword-grid">
                            <div class="keyword-tag">日期格式</div>
                            <div class="keyword-tag">时间格式</div>
                            <div class="keyword-tag">地点标准化</div>
                            <div class="keyword-tag">质量验证</div>
                        </div>
                    </div>

                    <div class="feature-section">
                        <div class="feature-title">输出格式</div>
                        <div class="output-format">
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [接机/送机]
other: [其他信息和备注]
ota_reference_number: [OTA参考号]
processing_notes: [处理说明]
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能选择规则 -->
        <div class="architecture-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">⚙️ 智能选择规则</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h3 style="color: #3498db; margin-bottom: 10px;">🚗 车型自动匹配</h3>
                    <div class="processing-rules">
                        <strong>1-4人</strong> → 1号车型 (轿车/小型车)<br>
                        <strong>5-7人</strong> → 2号车型 (7座MPV)<br>
                        <strong>8-10人</strong> → 3号车型 (商务车/中巴)
                    </div>
                </div>
                <div>
                    <h3 style="color: #3498db; margin-bottom: 10px;">📂 服务分类映射</h3>
                    <div class="processing-rules">
                        <strong>接机</strong> → 1号子分类<br>
                        <strong>送机</strong> → 2号子分类<br>
                        <strong>包车</strong> → 3号子分类<br>
                        <strong>点对点</strong> → 4号子分类<br>
                        <strong>机场转乘</strong> → 5号子分类
                    </div>
                </div>
                <div>
                    <h3 style="color: #3498db; margin-bottom: 10px;">👥 后台用户分配</h3>
                    <div class="processing-rules">
                        <strong>chong_dealer</strong> → 2号后台用户<br>
                        <strong>gomyhire</strong> → 1号后台用户<br>
                        <strong>other</strong> → 默认用户
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统优势 -->
        <div class="architecture-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">🎯 系统核心优势</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div class="processing-rules">
                    <strong>🔄 双AI架构</strong><br>
                    DeepSeek + Gemini 确保高可用性
                </div>
                <div class="processing-rules">
                    <strong>🎯 智能识别</strong><br>
                    16种关键词模式精准识别
                </div>
                <div class="processing-rules">
                    <strong>🔧 自动纠错</strong><br>
                    智能日期修正和时间计算
                </div>
                <div class="processing-rules">
                    <strong>📁 多格式支持</strong><br>
                    文本、图片、混合格式全支持
                </div>
                <div class="processing-rules">
                    <strong>📊 实时监控</strong><br>
                    完整的性能监控和错误追踪
                </div>
                <div class="processing-rules">
                    <strong>🛡️ 容错机制</strong><br>
                    三层故障转移保障系统稳定
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div style="text-align: center; padding: 20px; color: rgba(255,255,255,0.8);">
            <p>📅 最后更新时间：2025-06-02 | 🔧 系统版本：v2.0.0</p>
        </div>
    </div>
</body>
</html>
